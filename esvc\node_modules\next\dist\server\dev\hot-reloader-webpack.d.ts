import type { NextConfigComplete } from '../config-shared';
import type { CustomRoutes } from '../../lib/load-custom-routes';
import type { Duplex } from 'stream';
import type { Telemetry } from '../../telemetry/storage';
import type { IncomingMessage, ServerResponse } from 'http';
import type { UrlObject } from 'url';
import type { RouteDefinition } from '../route-definitions/route-definition';
import { type webpack } from 'next/dist/compiled/webpack/webpack';
import getBaseWebpackConfig from '../../build/webpack-config';
import type { __ApiPreviewProps } from '../api-utils';
import ws from 'next/dist/compiled/ws';
import type { UnwrapPromise } from '../../lib/coalesced-function';
import type { VersionInfo } from './parse-version-info';
import { type NextJsHotReloaderInterface } from './hot-reloader-types';
import type { HMR_ACTION_TYPES } from './hot-reloader-types';
export declare function renderScriptError(res: ServerResponse, error: Error, { verbose }?: {
    verbose?: boolean | undefined;
}): Promise<{
    finished: true | undefined;
}>;
export declare const matchNextPageBundleRequest: import("../../shared/lib/router/utils/path-match").PatchMatcher;
export declare function getVersionInfo(): Promise<VersionInfo>;
export default class HotReloaderWebpack implements NextJsHotReloaderInterface {
    private hasAmpEntrypoints;
    private hasAppRouterEntrypoints;
    private hasPagesRouterEntrypoints;
    private dir;
    private buildId;
    private encryptionKey;
    private middlewares;
    private pagesDir?;
    private distDir;
    private webpackHotMiddleware?;
    private config;
    private clientStats;
    private clientError;
    private serverError;
    private hmrServerError;
    private serverPrevDocumentHash;
    private serverChunkNames?;
    private prevChunkNames?;
    private onDemandEntries?;
    private previewProps;
    private watcher;
    private rewrites;
    private fallbackWatcher;
    private hotReloaderSpan;
    private pagesMapping;
    private appDir?;
    private telemetry;
    private resetFetch;
    private versionInfo;
    private devtoolsFrontendUrl;
    private reloadAfterInvalidation;
    private isSrcDir;
    serverStats: webpack.Stats | null;
    edgeServerStats: webpack.Stats | null;
    multiCompiler?: webpack.MultiCompiler;
    activeWebpackConfigs?: Array<UnwrapPromise<ReturnType<typeof getBaseWebpackConfig>>>;
    constructor(dir: string, { config, isSrcDir, pagesDir, distDir, buildId, encryptionKey, previewProps, rewrites, appDir, telemetry, resetFetch, }: {
        config: NextConfigComplete;
        isSrcDir: boolean;
        pagesDir?: string;
        distDir: string;
        buildId: string;
        encryptionKey: string;
        previewProps: __ApiPreviewProps;
        rewrites: CustomRoutes['rewrites'];
        appDir?: string;
        telemetry: Telemetry;
        resetFetch: () => void;
    });
    run(req: IncomingMessage, res: ServerResponse, parsedUrl: UrlObject): Promise<{
        finished?: true;
    }>;
    setHmrServerError(error: Error | null): void;
    clearHmrServerError(): void;
    protected refreshServerComponents(hash: string): Promise<void>;
    onHMR(req: IncomingMessage, _socket: Duplex, head: Buffer, callback: (client: ws.WebSocket) => void): void;
    private clean;
    private getWebpackConfig;
    buildFallbackError(): Promise<void>;
    private tracedGetVersionInfo;
    start(): Promise<void>;
    invalidate({ reloadAfterInvalidation }?: {
        reloadAfterInvalidation: boolean;
    }): void;
    getCompilationErrors(page: string): Promise<Error[]>;
    send(action: HMR_ACTION_TYPES): void;
    ensurePage({ page, clientOnly, appPaths, definition, isApp, url, }: {
        page: string;
        clientOnly: boolean;
        appPaths?: ReadonlyArray<string> | null;
        isApp?: boolean;
        definition?: RouteDefinition;
        url?: string;
    }): Promise<void>;
    close(): void;
}
